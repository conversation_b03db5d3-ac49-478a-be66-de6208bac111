/* HomePage Styles */

.homePage {
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--color-primary-brand) 0%, var(--color-secondary-brand) 100%);
  color: white;
  padding: var(--space-xxxl) 0;
  text-align: center;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-lg);
  line-height: var(--line-height-heading);
}

.heroSubtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-xl);
  opacity: 0.9;
  line-height: var(--line-height-body);
}

.heroButtons {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* Buttons */
.primaryButton {
  background-color: var(--color-accent);
  color: var(--color-text-primary);
  border: none;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 44px;
}

.primaryButton:hover {
  background-color: #c19660;
  transform: translateY(-1px);
}

.secondaryButton {
  background-color: transparent;
  color: white;
  border: 2px solid white;
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 44px;
}

.secondaryButton:hover {
  background-color: white;
  color: var(--color-primary-brand);
}

/* Section Styles */
.storeSection,
.categoriesSection,
.brandSection,
.newsletterSection {
  padding: var(--space-xxxl) 0;
}

.sectionTitle {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-semibold);
  text-align: center;
  margin-bottom: var(--space-xl);
  color: var(--color-text-primary);
}

/* Store Section */
.storeGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-xl);
  max-width: 800px;
  margin: 0 auto;
}

.storeCard {
  background-color: var(--color-background-secondary);
  padding: var(--space-xl);
  border-radius: var(--border-radius-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.storeCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.storeCard h3 {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-md);
  color: var(--color-primary-brand);
}

.storeCard p {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-lg);
}

.storeButtons {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

.shopButton {
  background-color: var(--color-primary-brand);
  color: white;
  border: none;
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  min-height: 40px;
}

.shopButton:hover {
  background-color: var(--color-secondary-brand);
}

/* Categories Section */
.categoriesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
}

.categoryCard {
  background-color: var(--color-background-secondary);
  padding: var(--space-xl);
  border-radius: var(--border-radius-lg);
  text-align: center;
  transition: all var(--transition-fast);
  cursor: pointer;
  border: 2px solid transparent;
}

.categoryCard:hover {
  transform: translateY(-2px);
  border-color: var(--color-primary-brand);
  box-shadow: var(--shadow-md);
}

.categoryCard h3 {
  font-size: var(--font-size-h5);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-sm);
  color: var(--color-primary-brand);
}

.categoryCard p {
  color: var(--color-text-secondary);
  margin: 0;
}

/* Brand Section */
.brandSection {
  background-color: var(--color-background-secondary);
}

.brandDescription {
  font-size: var(--font-size-lg);
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--space-xl);
  line-height: var(--line-height-body);
  color: var(--color-text-primary);
}

.brandButtons {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

.brandButtons .secondaryButton {
  color: var(--color-primary-brand);
  border-color: var(--color-primary-brand);
}

.brandButtons .secondaryButton:hover {
  background-color: var(--color-primary-brand);
  color: white;
}

/* Newsletter Section */
.newsletterSection {
  background-color: var(--color-primary-brand);
  color: white;
  text-align: center;
}

.newsletterSection .sectionTitle {
  color: white;
}

.newsletterSection p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-xl);
  opacity: 0.9;
}

.newsletterForm {
  display: flex;
  gap: var(--space-md);
  max-width: 500px;
  margin: 0 auto;
  flex-wrap: wrap;
  justify-content: center;
}

.emailInput {
  flex: 1;
  min-width: 250px;
  padding: var(--space-md);
  border: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-base);
  background-color: white;
  color: var(--color-text-primary);
}

.emailInput:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

/* Responsive Design */
@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-lg);
  }
  
  .storeGrid {
    grid-template-columns: 1fr 1fr;
  }
  
  .newsletterForm {
    flex-wrap: nowrap;
  }
  
  .emailInput {
    min-width: auto;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-xl);
  }
}
