import React from 'react';
import styles from './HomePage.module.css';

const HomePage: React.FC = () => {
  return (
    <div className={styles.homePage}>
      {/* Hero Section */}
      <section className={styles.hero}>
        <div className={styles.container}>
          <div className={styles.heroContent}>
            <h1 className={styles.heroTitle}>
              Premium Cannabis Extracts & Products
            </h1>
            <p className={styles.heroSubtitle}>
              Craft-quality cannabis products where cutting-edge technology 
              meets passionate cultivation. Serving Massachusetts with excellence.
            </p>
            <div className={styles.heroButtons}>
              <button className={styles.primaryButton}>Shop Now</button>
              <button className={styles.secondaryButton}>View Featured</button>
            </div>
          </div>
        </div>
      </section>

      {/* Store Finder Section */}
      <section className={styles.storeSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>Our Dispensary Locations</h2>
          <div className={styles.storeGrid}>
            <div className={styles.storeCard}>
              <h3>Hadley Dispensary</h3>
              <p>123 Main Street, Hadley, MA 01035</p>
              <div className={styles.storeButtons}>
                <button className={styles.shopButton}>Shop Med</button>
                <button className={styles.shopButton}>Shop Rec</button>
              </div>
            </div>
            <div className={styles.storeCard}>
              <h3>Bernardston Dispensary</h3>
              <p>456 Route 5, Bernardston, MA 01337</p>
              <div className={styles.storeButtons}>
                <button className={styles.shopButton}>Shop Rec</button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Product Categories Section */}
      <section className={styles.categoriesSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>Shop by Category</h2>
          <div className={styles.categoriesGrid}>
            <div className={styles.categoryCard}>
              <h3>Flower</h3>
              <p>Premium cannabis flower</p>
            </div>
            <div className={styles.categoryCard}>
              <h3>Pre-rolls</h3>
              <p>Ready-to-smoke joints</p>
            </div>
            <div className={styles.categoryCard}>
              <h3>Edibles</h3>
              <p>Cannabis-infused treats</p>
            </div>
            <div className={styles.categoryCard}>
              <h3>Extracts</h3>
              <p>Concentrated cannabis products</p>
            </div>
            <div className={styles.categoryCard}>
              <h3>Tinctures</h3>
              <p>Liquid cannabis extracts</p>
            </div>
            <div className={styles.categoryCard}>
              <h3>Vapes</h3>
              <p>Vaporizer cartridges</p>
            </div>
          </div>
        </div>
      </section>

      {/* Brand Story Section */}
      <section className={styles.brandSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>Where Technology Meets Passion in Craft Cannabis</h2>
          <p className={styles.brandDescription}>
            At Apothecary Extracts, we combine cutting-edge extraction technology 
            with passionate cultivation to create the finest cannabis products in Massachusetts. 
            Our vertically integrated approach ensures quality from seed to sale.
          </p>
          <div className={styles.brandButtons}>
            <button className={styles.secondaryButton}>Our Cultivation</button>
            <button className={styles.secondaryButton}>View Strains</button>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className={styles.newsletterSection}>
        <div className={styles.container}>
          <h2 className={styles.sectionTitle}>Stay Updated</h2>
          <p>Get the latest news, deals, and educational content delivered to your inbox.</p>
          <form className={styles.newsletterForm}>
            <input 
              type="email" 
              placeholder="Enter your email address"
              className={styles.emailInput}
              required
            />
            <button type="submit" className={styles.primaryButton}>
              Sign Up Today
            </button>
          </form>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
